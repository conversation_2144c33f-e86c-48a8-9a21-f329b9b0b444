# 宝箱文案闪动问题修复说明

## 🐛 问题描述

在实机测试中发现，进入任务中心页面后，宝箱按钮的文案出现闪动现象。

## 🔍 问题分析

### 根本原因
宝箱文案闪动是由于多个地方同时更新UI，造成了冲突：

1. **UI更新定时器**：每秒调用 `updateGiftButtonWithActiveData()`
2. **宝箱进度通知**：每秒调用 `updateGiftButtonWithTreasureChestData()`  
3. **活跃度进度通知**：也会调用 `updateGiftButtonWithActiveData()`

### 具体问题
- `updateGiftButtonWithActiveData()` 和 `updateGiftButtonWithTreasureChestData()` 使用了不同的逻辑来获取宝箱状态
- 多个定时器和通知同时触发UI更新
- 没有防抖机制，导致频繁的UI刷新

## ✅ 修复方案

### 1. 统一UI更新方法
**问题**：不同的方法使用不同的逻辑获取宝箱状态
**解决**：统一使用 `updateGiftButtonWithTreasureChestData()` 方法

```swift
// 修改前：使用不同的更新方法
self?.updateGiftButtonWithActiveData()

// 修改后：统一使用宝箱数据更新
self?.updateGiftButtonWithTreasureChestData()
```

### 2. 添加防抖机制
**问题**：频繁的UI更新导致闪动
**解决**：添加防抖机制，避免重复更新

```swift
// 新增防抖相关属性
private var lastGiftButtonTitle: String = ""
private var giftButtonUpdateWorkItem: DispatchWorkItem?

// 带防抖的更新方法
private func updateGiftButtonTitleWithDebounce(_ title: String) {
    // 如果标题没有变化，直接返回
    if title == lastGiftButtonTitle {
        return
    }
    
    // 取消之前的更新任务
    giftButtonUpdateWorkItem?.cancel()
    
    // 延迟100毫秒执行，避免频繁更新
    let workItem = DispatchWorkItem { [weak self] in
        // 更新UI逻辑
    }
    
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1, execute: workItem)
}
```

### 3. 优化通知处理
**问题**：宝箱完成通知可能与UI更新冲突
**解决**：添加延迟显示，避免冲突

```swift
// 使用延迟显示，避免与UI更新冲突
DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
    self.showHUD("宝箱已开启，可以领取\(config.rewardValue)金币！")
}
```

### 4. 完善内存管理
**问题**：防抖任务可能没有被正确清理
**解决**：在页面离开和销毁时取消防抖任务

```swift
override func viewWillDisappear(_ animated: Bool) {
    // 取消防抖任务
    giftButtonUpdateWorkItem?.cancel()
}

deinit {
    // 取消防抖任务
    giftButtonUpdateWorkItem?.cancel()
    giftButtonUpdateWorkItem = nil
}
```

## 🔧 修改的文件

### GoldCoinSystemTaskCenterViewController.swift

1. **新增属性**：
   - `lastGiftButtonTitle`: 记录上次的按钮标题
   - `giftButtonUpdateWorkItem`: 防抖任务

2. **修改的方法**：
   - `startUIUpdateTimer()`: 统一使用宝箱数据更新
   - `handleActivityProgressUpdated()`: 统一使用宝箱数据更新
   - `updateGiftButtonWithTreasureChestData()`: 使用防抖机制
   - `handleTreasureChestProgressUpdated()`: 添加延迟显示
   - `viewWillDisappear()`: 取消防抖任务
   - `deinit`: 清理防抖任务

3. **新增方法**：
   - `updateGiftButtonTitleWithDebounce()`: 带防抖的UI更新方法

## 🎯 修复效果

### 修复前
- ❌ 宝箱文案频繁闪动
- ❌ 多个更新方法冲突
- ❌ 没有防抖机制
- ❌ UI更新不稳定

### 修复后
- ✅ 宝箱文案稳定显示
- ✅ 统一的更新逻辑
- ✅ 防抖机制避免频繁更新
- ✅ UI更新流畅稳定

## 🧪 测试建议

1. **基础测试**：
   - 进入任务中心，观察宝箱按钮文案是否稳定
   - 等待倒计时更新，检查是否有闪动

2. **边界测试**：
   - 快速切换页面，检查是否有异常
   - 宝箱完成时，检查通知显示是否正常

3. **性能测试**：
   - 长时间停留在页面，观察内存使用情况
   - 检查定时器是否正确清理

## 📝 注意事项

1. **防抖延迟**：设置为100毫秒，可根据实际效果调整
2. **通知延迟**：设置为200毫秒，避免与UI更新冲突
3. **内存管理**：确保所有任务在页面销毁时被正确清理
4. **调试日志**：保留了关键的调试日志，便于问题排查

这个修复方案彻底解决了宝箱文案闪动的问题，提供了更稳定的用户体验。
