# 宝箱计时器增量上报实现说明

## 🎯 需求理解

根据你的说明，宝箱系统的上报机制应该是：

1. **服务器端是累计的**：`viewingSeconds` 是服务器记录的总活跃时长
2. **前端上报不累计**：每次上报的是增量时间，上报成功后前端计时器清零
3. **倒计时计算**：剩余时间 = `conditionValue - viewingSeconds`

## 🔧 实现方案

### 1. 数据结构调整

**修改前**：
```swift
private var treasureChestTotalSeconds: Int = 0  // 总时长
```

**修改后**：
```swift
private var treasureChestTargetSeconds: Int = 0      // 目标时长 (conditionValue)
private var serverViewingSeconds: Int = 0            // 服务器已记录时长 (viewingSeconds)
private var frontendAccumulatedSeconds: Int = 0      // 前端累计时长（用于上报）
```

### 2. 倒计时逻辑重构

**核心计算公式**：
```swift
// 计算总的活跃时长：服务器已记录 + 前端累计
let totalActiveSeconds = serverViewingSeconds + frontendAccumulatedSeconds

// 计算剩余时间：目标时长 - 总活跃时长
treasureChestRemainingSeconds = max(0, treasureChestTargetSeconds - totalActiveSeconds)
```

### 3. 增量上报机制

**上报逻辑**：
```swift
// 只上报前端累计的增量时间
let incrementalSeconds = frontendAccumulatedSeconds

if incrementalSeconds > 0 {
    APIManager.shared.reportActiveProgress(activeSeconds: incrementalSeconds) { result in
        // 上报成功后清零前端累计时长
        self?.handleProgressReportSuccess(data)
    }
}
```

**上报成功处理**：
```swift
private func handleProgressReportSuccess(_ data: ActiveProgressData) {
    // 更新服务器已记录的时长
    serverViewingSeconds = data.unclaimedActiveRewards?.viewingSeconds ?? 0
    
    // 清零前端累计时长（因为已经上报到服务器）
    frontendAccumulatedSeconds = 0
    
    // 重新计算剩余时间
    treasureChestRemainingSeconds = max(0, treasureChestTargetSeconds - serverViewingSeconds)
    
    // 重置前端计时器开始时间
    treasureChestStartTime = Date()
}
```

## 📊 数据流示例

### 场景：10秒宝箱，服务器已记录5秒

**初始状态**：
- `conditionValue` = 10秒（目标）
- `viewingSeconds` = 5秒（服务器已记录）
- `treasureChestTargetSeconds` = 10秒
- `serverViewingSeconds` = 5秒
- `frontendAccumulatedSeconds` = 0秒
- `treasureChestRemainingSeconds` = 10 - 5 = 5秒

**运行3秒后**：
- `frontendAccumulatedSeconds` = 3秒
- 总活跃时长 = 5 + 3 = 8秒
- `treasureChestRemainingSeconds` = 10 - 8 = 2秒

**5秒上报时**：
- 上报增量：3秒
- 服务器更新：`viewingSeconds` = 8秒
- 前端清零：`frontendAccumulatedSeconds` = 0秒
- 剩余时间：10 - 8 = 2秒

**再运行2秒完成**：
- `frontendAccumulatedSeconds` = 2秒
- 总活跃时长 = 8 + 2 = 10秒
- `treasureChestRemainingSeconds` = 10 - 10 = 0秒（完成）

## 🔄 关键修改点

### ActivityProgressManager.swift

1. **属性调整**：
   - 新增 `treasureChestTargetSeconds`：目标时长
   - 新增 `serverViewingSeconds`：服务器已记录时长
   - 新增 `frontendAccumulatedSeconds`：前端累计时长
   - 移除 `treasureChestTotalSeconds`：不再需要

2. **初始化逻辑**：
   ```swift
   treasureChestTargetSeconds = config.conditionValue
   serverViewingSeconds = config.viewingSeconds ?? 0
   frontendAccumulatedSeconds = 0
   treasureChestRemainingSeconds = max(0, treasureChestTargetSeconds - serverViewingSeconds)
   ```

3. **倒计时更新**：
   ```swift
   let frontendElapsedSeconds = Int(Date().timeIntervalSince(startTime))
   frontendAccumulatedSeconds = frontendElapsedSeconds
   
   let totalActiveSeconds = serverViewingSeconds + frontendAccumulatedSeconds
   treasureChestRemainingSeconds = max(0, treasureChestTargetSeconds - totalActiveSeconds)
   ```

4. **增量上报**：
   ```swift
   // 定期上报：只上报前端累计的增量时间
   APIManager.shared.reportActiveProgress(activeSeconds: frontendAccumulatedSeconds)
   
   // 上报成功后：更新服务器时长，清零前端累计
   serverViewingSeconds = newViewingSeconds
   frontendAccumulatedSeconds = 0
   treasureChestStartTime = Date() // 重置计时器
   ```

5. **状态持久化**：
   - 保存 `treasureChestTargetSeconds`
   - 保存 `serverViewingSeconds`
   - 保存 `frontendAccumulatedSeconds`

## ✅ 优势

1. **时间同步**：避免前端和服务器时间不一致的问题
2. **增量上报**：减少网络传输，提高效率
3. **状态恢复**：app重启后能正确恢复倒计时状态
4. **容错机制**：上报失败不影响本地计时器运行
5. **精确计算**：基于服务器权威数据计算剩余时间

## 🧪 测试场景

1. **正常流程**：完整的倒计时和上报流程
2. **中断恢复**：app退出重启后状态恢复
3. **网络异常**：上报失败时的处理
4. **多次上报**：定期上报的增量计算
5. **边界条件**：0秒宝箱、已完成宝箱等

这个实现完全符合你描述的需求，确保了前端和服务器数据的一致性，同时提供了可靠的增量上报机制。
